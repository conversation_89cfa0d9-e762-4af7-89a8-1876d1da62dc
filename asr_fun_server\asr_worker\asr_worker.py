import os
import logging
import time
import ffmpeg
from io import Bytes<PERSON>
from minio import Minio
from funasr import AutoModel
from celery.signals import worker_process_init
from celery import Task
from celery.utils.log import get_task_logger

from celery_app import celery_app

# --- Logging Configuration ---
# Use get_task_logger for logging within tasks
logger = get_task_logger(__name__)

# --- MinIO Client Configuration ---
# This is stateless and can be defined globally
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "localhost:9000").strip()
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin").strip()
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin").strip()

minio_client = Minio(
    MINIO_ENDPOINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=False
)

# --- ASR Model Parameters ---
param_dict = {
    "sentence_timestamp": True, 
    "batch_size_s": 120,
    "max_batch_size": 16
}
HOTWORD_PATH = os.getenv("HOTWORD_PATH", "hotwords.txt")
if os.path.exists(HOTWORD_PATH):
    with open(HOTWORD_PATH, "r", encoding="utf-8") as f:
        hotword = " ".join([line.strip() for line in f])
    logger.info(f"Loaded hotwords: {hotword}")
    param_dict["hotword"] = hotword


# --- Custom Task Class for ASR ---
class ASRTask(Task):
    """
    A custom Celery Task class that holds the loaded FunASR model.
    The model is loaded once per worker process and attached to this class.
    """
    _model = None

    @property
    def model(self):
        """
        Property to access the model. Ensures the model is loaded.
        The linter can understand this direct access pattern.
        """
        # The model should have been loaded by the signal handler.
        # This property access makes the dependency explicit.
        return self._model

    @classmethod
    def set_model(cls, model):
        """Class method to set the loaded model from the signal handler."""
        cls._model = model

# --- Signal Handler to Load Model ---
@worker_process_init.connect
def init_model_handler(**kwargs):
    """
    This function is called when a Celery worker process starts.
    It loads the model and attaches it to the ASRTask class.
    """
    logger.info("Worker process initializing... Loading FunASR model.")
    
    ASR_MODEL_PATH = os.getenv("ASR_MODEL_PATH", "/models/paraformer-zh")
    VAD_MODEL_PATH = os.getenv("VAD_MODEL_PATH", "/models/fsmn-vad")
    PUNC_MODEL_PATH = os.getenv("PUNC_MODEL_PATH", "/models/ct-punc-c")
    SPK_MODEL_PATH = os.getenv("SPK_MODEL_PATH", "/models/cam++")
    
    DEVICE = "cuda" if int(os.getenv("NGPU", 1)) > 0 else "cpu"
    NCPU = int(os.getenv("NCPU", 4))

    loaded_model = AutoModel(
        model=ASR_MODEL_PATH,
        vad_model=VAD_MODEL_PATH,
        punc_model=PUNC_MODEL_PATH,
        spk_model=SPK_MODEL_PATH,
        ngpu=int(os.getenv("NGPU", 1)),
        ncpu=NCPU,
        device=DEVICE,
        disable_pbar=True,
        disable_log=True,
        disable_update=True,
    )
    
    # Attach the loaded model to our custom task class
    ASRTask.set_model(loaded_model)
    logger.info(f"Model loaded and attached to ASRTask on device: {DEVICE}")


# --- Celery Task Definition ---
# Use the `base=ASRTask` argument to employ our custom class.
@celery_app.task(bind=True, base=ASRTask, name='asr_worker.asr_task')
def asr_task(self: ASRTask, bucket_name: str, object_name: str):
    """
    Celery task for ASR processing. `self` is an instance of ASRTask.
    """
    task_id = self.request.id
    queue_name = self.request.delivery_info['routing_key']
    logger.info(f"[{task_id}] Received task from queue '{queue_name}': process {object_name} from bucket {bucket_name}")

    try:
        # 1. Download audio from MinIO
        response = minio_client.get_object(bucket_name, object_name)
        audio_data = response.read()
        response.close()
        response.release_conn()

        # 2. Convert audio format with ffmpeg
        try:
            audio_bytes, _ = (
                ffmpeg.input("pipe:0")
                .output("-", format="s16le", acodec="pcm_s16le", ac=1, ar=16000)
                .run(input=audio_data, capture_stdout=True, capture_stderr=True)
            )
        except ffmpeg.Error as e:
            logger.error(f"[{task_id}] FFmpeg error during audio conversion: {e.stderr.decode()}", exc_info=True)
            raise Exception(f"FFmpeg error: {e.stderr.decode()}")

        # 3. Abort if audio is silent
        if not audio_bytes or all(v == 0 for v in audio_bytes):
            logger.warning(f"[{task_id}] Converted audio is silent or empty. Aborting task.")
            return {"status": 2, "msg": "Input audio appears to be silent or invalid.", "data": {}}

        # 4. Run ASR inference using the model from the task class
        start_time = time.time()
        # The linter can now trace `self.model` back to the ASRTask class
        rec_results = self.model.generate(input=audio_bytes, is_final=True, **param_dict)
        end_time = time.time()
        logger.info(f"[{task_id}] ASR model processing finished in {end_time - start_time:.2f}s.")

        # 5. Process and return results
        if not rec_results:
            logger.warning(f"[{task_id}] Recognition result is empty.")
            return {"status": 1, "msg": "Recognition result is empty", "data": {}}

        rec_result = rec_results[0]
        sentences = [
            {
                "text": s.get("text", ""),
                "start": s.get("start"),
                "end": s.get("end"),
                "timestamp": s.get("timestamp"),
                "spk": s.get("spk", 0),
                "infer_time": int(end_time * 1000 - start_time * 1000)
            }
            for s in rec_result.get("sentence_info", [])
        ]
        result_data = {"text": rec_result.get("text", ""), "sentences": sentences}
        logger.info(f"[{task_id}] Recognition successful.")
        
        return {"status": 0, "msg": "success", "data": result_data}

    except Exception as e:
        logger.error(f"[{task_id}] An unexpected error occurred: {e}", exc_info=True)
        raise e

import uuid
import hashlib
import json

from pydantic import BaseModel, <PERSON>
from fastapi import Body
from fastapi.responses import StreamingResponse
import uvicorn
from celery.result import AsyncResult

from celery_app import celery_app
from common_lib.base_server import BaseApiServer

class TTSRequest(BaseModel):
    text: str = Field(..., description="Text to be converted to speech.")
    speaker: str = Field('zh-CN-XiaoxiaoNeural', description="Identifier for the speaker voice to be used.")
class TtsApiServer(BaseApiServer):
    def __init__(self):
        super().__init__(
            service_name="TTS",
            celery_app=celery_app,
            minio_bucket="tts-index-audio-bucket",
            redis_cache_db=4,
            log_filename="api_server.log"
        )
        self._register_tts_routes()

    async def _get_result_implementation(self, task_id: str):
        """
        Overrides the base implementation to stream audio from MinIO.
        """
        self.logger.info(f"Querying result for task ID: {task_id}")
        task_result = AsyncResult(task_id, app=self.celery_app)

        if task_result.state == 'SUCCESS':
            minio_object_name = task_result.result["data"]["object_name"]
            self.logger.info(f"Task {task_id} succeeded. Fetching audio from MinIO: {minio_object_name}")
            try:
                # Get the audio stream from MinIO
                audio_stream = self.minio_client.get_object(self.minio_bucket, minio_object_name)
                # Return a streaming response
                return StreamingResponse(audio_stream.stream(32*1024), media_type="audio/mpeg")
            except Exception as e:
                self.logger.error(f"Error fetching object from MinIO for task {task_id}: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to retrieve audio data.")
        else:
            # For any other state, use the base class implementation
            return await super()._get_result_implementation(task_id)

    def _register_tts_routes(self):
        @self.app.post("/llm/tts/text_to_speech")
        async def text_to_speech(request_data: TTSRequest):
            if not self.queue_manager.has_active_queues:
                return self.wrap_response({}, -1, "No active workers available.")

            try:
                target_queue = self.queue_manager.get_next_queue()
                task_id = str(uuid.uuid4())
                self.logger.info(f"Cache MISS. Routing task {task_id} to queue: {target_queue}")

                task_kwargs = {
                    "text": request_data.text,
                    "speaker": request_data.speaker,
                    "minio_bucket": self.minio_bucket
                }

                self.celery_app.send_task(
                    'tts_worker.tts_task',
                    kwargs=task_kwargs,  # Use kwargs to pass arguments by name
                    task_id=task_id,
                    expires=120,
                    time_limit=300,
                    queue=target_queue
                )
                self.logger.info(f"Task {task_id} sent to queue '{target_queue}'.")

                return self.wrap_response(data_dict={"task_id": task_id}, msg="Task received.")
            except Exception as e:
                self.logger.error(f"Error submitting TTS task: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to submit TTS task.")

        @self.app.post("/llm/tts/fetch_result")
        async def get_result(task_id: str = Body(..., embed=True)):
            return await self._get_result_implementation(task_id)
        
        @self.app.post("/llm/tts/query_speakers")
        async def query_speakers(gender: str = Body(None, embed=True), language: str = Body(None, embed=True)):
            speaker_infos = [
                ("zh-CN-XiaoxiaoNeural", "zh-CN", "Female")
                ("zh-CN-Child01Neural", "zh-CN", "Male")
            ]
            speaker_names = [name for name, lang, g in speaker_infos if (gender is None or g == gender) and (language is None or lang == language)]
            return self.wrap_response(data_dict={"speakers": speaker_names})
        


app = TtsApiServer().app

if __name__ == '__main__':
    # This block is for local development and debugging
    uvicorn.run("api_server:app", host="0.0.0.0", port=50102, reload=True)

import uuid
import os
import hashlib
import json
from io import By<PERSON><PERSON>
from celery.exceptions import TimeoutError
import uvicorn

from fastapi import File, UploadFile
from minio.error import S3Error
from fastapi import Body

from celery_app import celery_app
from common_lib.base_server import BaseApiServer

class OCRApiServer(BaseApiServer):
    def __init__(self):
        super().__init__(
            service_name="OCR",
            celery_app=celery_app,
            minio_bucket= "ocr-monkey-file-bucket",
            redis_cache_db=os.getenv("OCR_REDIS_CACHE_DB", "5").strip(),
            log_filename="api_server.log"
        )
        self._register_ocr_routes()

    def _register_ocr_routes(self):

        @self.app.post("/llm/ocr/doc_to_md")
        async def doc_to_md(file: UploadFile = File(..., description="doc file"), split_pages = Body(False, embed=True), return_type = Body("text", embed=True)):
            allowed_extensions = {'.pdf', '.jpg', '.jpeg', '.png'}
            file_ext_with_dot = os.path.splitext(file.filename)[1].lower() if file.filename else ''
            
            if file_ext_with_dot not in allowed_extensions:
                return self.wrap_response({}, -1, f"Unsupported file type: {file_ext_with_dot}. Allowed: {', '.join(allowed_extensions)}")

            if not self.queue_manager.has_active_queues:
                return self.wrap_response({}, -1, "No active workers available.")

            try:
                target_queue = self.queue_manager.get_next_queue()
                task_id = str(uuid.uuid4())
                suffix = file_ext_with_dot[1:]
                object_name = f"{task_id}.{suffix}"
                self.logger.info(f"ASYNC request. Routing to queue '{target_queue}'.")

                file_content = await file.read()
                self.minio_client.put_object(
                    self.minio_bucket, object_name, BytesIO(file_content),
                    length=len(file_content), content_type=file.content_type
                )
                self.logger.info(f"File '{object_name}' uploaded.")
                task_args = (
                    self.minio_bucket,
                    object_name,
                    split_pages,
                    return_type
                )
                self.celery_app.send_task(
                    'ocr_worker.ocr_task',
                    args=task_args,
                    task_id=task_id,
                    expires=60,
                    time_limit=300,
                    queue=target_queue
                )
                self.logger.info(f"Task {task_id} sent to queue '{target_queue}'.")

                return self.wrap_response(data_dict={"task_id": task_id}, msg="Task received.")
            
            except S3Error as e:
                self.logger.error(f"MinIO S3 Error: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to upload file to storage.")
            except Exception as e:
                self.logger.error(f"Error processing async request: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to submit recognition task.")

        @self.app.post("/llm/ocr/fetch_result")
        async def get_result(task_id: str = Body(..., embed=True)):
            # Note: ASR does not write to cache on result fetch, only on sync completion.
            # This implementation is simpler and just fetches the result.
            return await self._get_result_implementation(task_id)


app = OCRApiServer().app

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)

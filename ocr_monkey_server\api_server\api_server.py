import uuid
import os
import hashlib
import json
from io import By<PERSON><PERSON>
from celery.exceptions import TimeoutError
import uvicorn

from fastapi import File, UploadFile
from minio.error import S3Error
from fastapi import Body

from celery_app import celery_app
from common_lib.base_server import BaseApiServer

class OCRApiServer(BaseApiServer):
    def __init__(self):
        super().__init__(
            service_name="OCR",
            celery_app=celery_app,
            minio_bucket= "ocr-monkey-file-bucket",
            redis_cache_db=os.getenv("OCR_REDIS_CACHE_DB", "5").strip(),
            log_filename="api_server.log"
        )
        self._register_ocr_routes()

    def _register_ocr_routes(self):

        @self.app.post("/llm/ocr/recognition_sync")
        async def sync_recognition(file: UploadFile = File(..., description="document file"),
                                 split_pages = Body(False, embed=True),
                                 return_type = Body("markdown", embed=True)):
            """Synchronous OCR recognition endpoint"""
            file_content = await file.read()
            file_hash = None

            # Check cache for sync requests
            if self.redis_cache_client and return_type in ['text', 'formula', 'table']:
                try:
                    file_hash = hashlib.sha256(file_content).hexdigest()
                    cached_result = self.redis_cache_client.get(file_hash)
                    if cached_result:
                        self.logger.info(f"Cache HIT for file hash: {file_hash}.")
                        result_data = json.loads(cached_result)
                        return self.wrap_response(result_data, msg="Success (from cache)")
                except Exception as e:
                    self.logger.error(f"Redis cache check failed: {e}", exc_info=True)

            # Validate file type
            allowed_extensions = {'.pdf', '.jpg', '.jpeg', '.png'}
            file_ext_with_dot = os.path.splitext(file.filename)[1].lower() if file.filename else ''

            if file_ext_with_dot not in allowed_extensions:
                return self.wrap_response({}, -1, f"Unsupported file type: {file_ext_with_dot}. Allowed: {', '.join(allowed_extensions)}")

            if not self.queue_manager.has_active_queues:
                return self.wrap_response({}, -1, "No active workers available.")

            try:
                target_queue = self.queue_manager.get_next_queue()
                self.logger.info(f"Cache MISS. Routing task to queue: {target_queue}")

                suffix = file_ext_with_dot[1:]
                object_name = f"{str(uuid.uuid4())}.{suffix}"

                self.minio_client.put_object(
                    self.minio_bucket, object_name, BytesIO(file_content),
                    length=len(file_content), content_type=file.content_type
                )
                self.logger.info(f"File '{object_name}' uploaded. Submitting task...")

                task = self.celery_app.send_task(
                    'ocr_worker.ocr_task',
                    args=[self.minio_bucket, object_name, split_pages, return_type],
                    expires=120, time_limit=600, queue=target_queue
                )
                self.logger.info(f"Task {task.id} submitted. Waiting for result...")

                result = await self.get_task_result_async(task, timeout=120)
                self.logger.info(f"Task {task.id} completed.")

                # Cache result for single task recognition
                if self.redis_cache_client and file_hash and result.get("status", -1) == 0 and return_type in ['text', 'formula', 'table']:
                    try:
                        data_to_cache = result.get("data", {})
                        self.redis_cache_client.set(
                            file_hash, json.dumps(data_to_cache), ex=self.cache_expiration_seconds
                        )
                        self.logger.info(f"Result for file hash {file_hash} stored in cache.")
                    except Exception as e:
                        self.logger.error(f"Failed to store result in Redis cache: {e}", exc_info=True)

                return self.wrap_response(result.get("data", {}), result.get("status", 0), result.get("msg", "success"))

            except TimeoutError:
                self.logger.error(f"Task {task.id} timed out.")
                return self.wrap_response({}, -1, "Processing timed out.")
            except S3Error as e:
                self.logger.error(f"MinIO S3 Error: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to upload file to storage.")
            except Exception as e:
                self.logger.error(f"Error processing sync request: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to submit recognition task.")

        @self.app.post("/llm/ocr/recognition")
        async def async_recognition(file: UploadFile = File(..., description="document file"),
                                  split_pages = Body(False, embed=True),
                                  return_type = Body("markdown", embed=True)):
            """Asynchronous OCR recognition endpoint"""
            # Validate file type
            allowed_extensions = {'.pdf', '.jpg', '.jpeg', '.png'}
            file_ext_with_dot = os.path.splitext(file.filename)[1].lower() if file.filename else ''

            if file_ext_with_dot not in allowed_extensions:
                return self.wrap_response({}, -1, f"Unsupported file type: {file_ext_with_dot}. Allowed: {', '.join(allowed_extensions)}")

            if not self.queue_manager.has_active_queues:
                return self.wrap_response({}, -1, "No active workers available.")

            try:
                target_queue = self.queue_manager.get_next_queue()
                task_id = str(uuid.uuid4())
                suffix = file_ext_with_dot[1:]
                object_name = f"{task_id}.{suffix}"
                self.logger.info(f"ASYNC request. Routing to queue '{target_queue}'.")

                file_content = await file.read()
                self.minio_client.put_object(
                    self.minio_bucket, object_name, BytesIO(file_content),
                    length=len(file_content), content_type=file.content_type
                )
                self.logger.info(f"File '{object_name}' uploaded.")

                self.celery_app.send_task(
                    'ocr_worker.ocr_task',
                    args=[self.minio_bucket, object_name, split_pages, return_type],
                    task_id=task_id,
                    expires=120,
                    time_limit=600,
                    queue=target_queue
                )
                self.logger.info(f"Task {task_id} sent to queue '{target_queue}'.")

                return self.wrap_response(data_dict={"task_id": task_id}, msg="Task received.")

            except S3Error as e:
                self.logger.error(f"MinIO S3 Error: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to upload file to storage.")
            except Exception as e:
                self.logger.error(f"Error processing async request: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to submit recognition task.")

        @self.app.post("/llm/ocr/fetch_result")
        async def get_result(task_id: str = Body(..., embed=True)):
            """Fetch result for asynchronous OCR task"""
            return await self._get_result_implementation(task_id)

        # Legacy endpoint for backward compatibility
        @self.app.post("/llm/ocr/doc_to_md")
        async def doc_to_md(file: UploadFile = File(..., description="doc file"),
                          split_pages = Body(False, embed=True),
                          return_type = Body("markdown", embed=True)):
            """Legacy endpoint - redirects to async recognition"""
            return await async_recognition(file, split_pages, return_type)


app = OCRApiServer().app

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)

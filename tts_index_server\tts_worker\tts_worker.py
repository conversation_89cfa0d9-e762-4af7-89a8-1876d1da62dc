import os
import time
from io import BytesIO
import ffmpeg
import numpy as np
from minio import Minio
from celery import Task
from celery.signals import worker_process_init
from celery.utils.log import get_task_logger

from indextts.infer import IndexTTS
from celery_app import celery_app

# --- Logging Configuration ---
logger = get_task_logger(__name__)

# --- MinIO Client Configuration ---
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "localhost:9000").strip()
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin").strip()
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin").strip()

minio_client = Minio(
    MINIO_ENDPOINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=False
)

# --- Custom Task Class for TTS ---
class TTSTask(Task):
    """
    A custom Celery Task class that holds the loaded IndexTTS model.
    The model is loaded once per worker process.
    """
    _model = None

    @property
    def model(self):
        """Property to access the model, ensuring it's loaded."""
        return self._model

    @classmethod
    def set_model(cls, model):
        """Class method to set the loaded model from the signal handler."""
        cls._model = model

# --- Signal Handler to Load Model ---
@worker_process_init.connect
def init_model_handler(**kwargs):
    """
    Called when a Celery worker process starts.
    It loads the TTS model and attaches it to the TTSTask class.
    """
    logger.info("Worker process initializing... Loading IndexTTS model.")
    
    # The model directory is expected to be mounted at /models in the container
    model_dir = "/models"
    config_path = os.path.join(model_dir, "config.yaml")

    if not os.path.exists(model_dir) or not os.path.exists(config_path):
        logger.error(f"Model directory '{model_dir}' or config '{config_path}' not found!")
        # This will prevent the worker from starting if the model is missing
        raise FileNotFoundError(f"TTS model assets not found at {model_dir}")

    try:
        loaded_model = IndexTTS(model_dir=model_dir, cfg_path=config_path)
        TTSTask.set_model(loaded_model)
        logger.info("IndexTTS model loaded and attached to TTSTask successfully.")
    except Exception as e:
        logger.error(f"Failed to load IndexTTS model: {e}", exc_info=True)
        raise

# --- Celery Task Definition ---
@celery_app.task(bind=True, base=TTSTask, name='tts_worker.tts_task')
def tts_task(self: TTSTask, text: str, speaker: str, minio_bucket: str):
    """
    Celery task for TTS processing.
    `self` is an instance of TTSTask, providing access to the loaded model.
    """
    task_id = self.request.id
    logger.info(f"[{task_id}] Received TTS task: speaker='{speaker}', text='{text[:50]}...'")

    try:
        # 1. Locate the speaker audio file
        # The speaker audio files are expected to be in the `speaker_audio` directory
        # This path needs to be correct inside the running container
        speaker_audio_dir = "speaker_audio"
        speaker_audio_path = os.path.join(speaker_audio_dir, f"{speaker}.wav")

        if not os.path.exists(speaker_audio_path):
            logger.error(f"[{task_id}] Speaker audio file not found at: {speaker_audio_path}")
            raise FileNotFoundError(f"Speaker '{speaker}' not found.")

        # 2. Run TTS inference
        start_time = time.time()
        # The `infer` method returns: sampling_rate, audio_data (numpy array)
        sampling_rate, audio_data = self.model.infer(speaker_audio_path, text, output_path=None)
        end_time = time.time()
        
        infer_duration = end_time - start_time
        audio_duration = len(audio_data) / sampling_rate
        logger.info(f"[{task_id}] TTS inference finished in {infer_duration:.2f}s. Generated {audio_duration:.2f}s of audio.")

        # 3. Convert audio data to MP3 format using ffmpeg
        # The audio_data from the model is already in np.int16 format.
        try:
            # Use ffmpeg to encode to mp3
            mp3_bytes, err = (
                ffmpeg
                .input('pipe:0', format='s16le', ac=1, ar=sampling_rate)
                # Using 'libmp3lame' for encoding, '-q:a 2' for high quality VBR
                .output('pipe:1', format='mp3', acodec='libmp3lame', q='2')
                .run(input=audio_data.tobytes(), capture_stdout=True, capture_stderr=True)
            )
            if err:
                logger.warning(f"[{task_id}] FFmpeg stderr when converting to MP3: {err.decode()}")

        except ffmpeg.Error as e:
            logger.error(f"[{task_id}] FFmpeg error during MP3 conversion: {e.stderr.decode()}", exc_info=True)
            raise Exception(f"FFmpeg MP3 encoding error: {e.stderr.decode()}")

        # 4. Upload the generated audio to MinIO
        # Ensure the bucket exists
        found = minio_client.bucket_exists(minio_bucket)
        if not found:
            minio_client.make_bucket(minio_bucket)
            logger.info(f"[{task_id}] Created MinIO bucket: {minio_bucket}")

        object_name = f"{task_id}.mp3"
        minio_client.put_object(
            minio_bucket,
            object_name,
            BytesIO(mp3_bytes),
            len(mp3_bytes),
            content_type='audio/mpeg'
        )
        logger.info(f"[{task_id}] Result uploaded to MinIO: '{minio_bucket}/{object_name}'")

        # 5. Return the MinIO object name on success
        # 返回一下推理时间
        data = {
            "object_name": object_name,
            "infer_duration": infer_duration,
            "audio_duration": audio_duration,
        }
        return {"status": 0, "msg": "success", "data": data}

    except FileNotFoundError as e:
        logger.error(f"[{task_id}] A required file was not found: {e}", exc_info=True)
        # Reraise to let the task fail with a specific state
        raise e
    except Exception as e:
        logger.error(f"[{task_id}] An unexpected error occurred during TTS processing: {e}", exc_info=True)
        # Reraise to mark the task as failed
        raise e

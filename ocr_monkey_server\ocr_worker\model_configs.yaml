device: cuda # cuda / cpu / mps (using `transformers` as backend)
weights:
  doclayout_yolo: Structure/doclayout_yolo_docstructbench_imgsz1280_2501.pt # or Structure/layout_zh.pt
  layoutreader: Relation
models_dir: /models
layout_config: 
  model: doclayout_yolo
  reader:
    name: layoutreader
chat_config:
  weight_path: /models/Recognition
  backend: lmdeploy # lmdeploy / vllm / transformers / api
  batch_size: 10 # active when using `transformers` as backend
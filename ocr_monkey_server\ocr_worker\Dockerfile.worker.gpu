# OCR Worker GPU Dockerfile with Python 3.10
# 构建速度虽然很慢，尤其是paddlepaddle的安装, 但可以成功，失败了多试几次
FROM nvidia/cuda:12.2.0-base-ubuntu22.04

# Step 1: Set environment variables to prevent interactive prompts during installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Step 2: Install system dependencies and Python 3.10
RUN apt-get update && apt-get install -y \
    software-properties-common \
    wget \
    curl \
    git \
    lsof \
    vim \
    unzip \
    fontconfig \
    fonts-noto-cjk \
    build-essential \
    libssl-dev \
    libffi-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncurses5-dev \
    libncursesw5-dev \
    xz-utils \
    tk-dev \
    libxml2-dev \
    libxmlsec1-dev \
    libffi-dev \
    liblzma-dev \
    && rm -rf /var/lib/apt/lists/*

# Step 3: Install Python 3.10 from deadsnakes PPA
RUN add-apt-repository ppa:deadsnakes/ppa && \
    apt-get update && \
    apt-get install -y \
    python3.10 \
    python3.10-dev \
    python3.10-distutils \
    python3.10-venv \
    && rm -rf /var/lib/apt/lists/*

# Step 4: Install pip for Python 3.10
RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python3.10

# Step 5: Create symbolic links for python and pip
RUN ln -sf /usr/bin/python3.10 /usr/bin/python3 && \
    ln -sf /usr/bin/python3.10 /usr/bin/python && \
    ln -sf /usr/local/bin/pip3.10 /usr/bin/pip3 && \
    ln -sf /usr/local/bin/pip3.10 /usr/bin/pip

# Step 6: Verify Python installation
RUN python --version && pip --version

# Step 7: Install Chinese and Japanese fonts
RUN wget -O /tmp/simsun.zip https://files.cnblogs.com/files/xiaochina/simsun.zip && \
    unzip /tmp/simsun.zip -d /tmp && \
    cp /tmp/simsun.ttc /usr/share/fonts/ && \
    chmod 644 /usr/share/fonts/simsun.ttc && \
    fc-cache -fv && \
    rm /tmp/simsun.zip /tmp/simsun.ttc



# Step 8: Set working directory
WORKDIR /workspace/ocr_monkey/ocr_worker

# Step 9: Copy requirements first to leverage Docker layer caching
COPY worker_requirements_gpu.txt .

# Step 10: Set CUDA version and install PyTorch
# Based on CUDA 12.2 base image, use CUDA 12.1 compatible PyTorch
# You can change this to 124 for CUDA 12.4 if needed:
# ENV CUDA_VERSION=124
ENV CUDA_VERSION=121
RUN pip install --upgrade pip setuptools wheel

# Step 11: Install PyTorch with CUDA support
# Refer to https://pytorch.org/get-started/previous-versions/ for version compatibility
RUN pip install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 \
    --index-url https://download.pytorch.org/whl/cu${CUDA_VERSION} \
    --no-cache-dir

# Step 12: Install lmdeploy
RUN pip install lmdeploy==0.8.0 --no-cache-dir

# Step 13: Install other Python dependencies
RUN pip install -r worker_requirements_gpu.txt -i https://mirrors.aliyun.com/pypi/simple/ --no-cache-dir

# Step 14: Copy your application code into the image
COPY . .

# Step 15: Create necessary directories and set permissions
RUN mkdir -p /tmp/ocr_results && \
    chmod 755 /tmp/ocr_results

# Step 16: Set environment variables for the application
ENV PYTHONPATH=/workspace/ocr_monkey/ocr_worker:$PYTHONPATH

# Step 17: Clear the entrypoint from the base image so our command becomes PID 1
ENTRYPOINT []
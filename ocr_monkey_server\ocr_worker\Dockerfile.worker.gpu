# 已经内置了python 3.10 git lsof torch vim ffmpeg
# 构建速度虽然很慢，尤其是paddlepaddle的安装, 但可以成功，失败了多试几次
FROM nvidia/cuda:12.2.0-base-ubuntu22.04:latest

# Step 2: Set environment variables to prevent interactive prompts during installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# Install Chinese and Japanese fonts
RUN apt-get update &&     apt-get install -y wget unzip fontconfig fonts-noto-cjk &&     wget -O /tmp/simsun.zip https://files.cnblogs.com/files/xiaochina/simsun.zip &&     unzip /tmp/simsun.zip -d /tmp &&     cp /tmp/simsun.ttc /usr/share/fonts/ &&     chmod 644 /usr/share/fonts/simsun.ttc &&     fc-cache -fv &&     rm /tmp/simsun.zip /tmp/simsun.ttc &&     apt-get clean &&     rm -rf /var/lib/apt/lists/*



# Step 5: Install TTSIndex and other Python dependencies
# Copy requirements first to leverage Docker layer caching
WORKDIR /workspace/ocr_monkey/ocr_worker
COPY worker_requirements_gpu.txt .

RUN pip install -r worker_requirements_gpu.txt -i https://mirrors.aliyun.com/pypi/simple/

# Step 6: Copy your application code into the image
COPY . .


# Clear the entrypoint from the base image so our command becomes PID 1
ENTRYPOINT []
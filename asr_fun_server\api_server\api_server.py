import uuid
import os
import hashlib
import json
from io import Bytes<PERSON>
from celery.exceptions import TimeoutError
import uvicorn

from fastapi import File, UploadFile
from minio.error import S3Error
from fastapi import Body

from celery_app import celery_app
from common_lib.base_server import BaseApiServer

class AsrApiServer(BaseApiServer):
    def __init__(self):
        super().__init__(
            service_name="ASR",
            celery_app=celery_app,
            minio_bucket= "asr-funasr-audio-bucket",
            redis_cache_db=int(os.getenv("ASR_REDIS_CACHE_DB", "2").strip()),
            log_filename="api_server.log"
        )
        self._register_asr_routes()

    def _register_asr_routes(self):
        @self.app.post("/llm/asr/recognition_sync")
        async def sync_recognition(file: UploadFile = File(..., description="audio file")):
            file_content = await file.read()
            file_hash = None

            if self.redis_cache_client:
                try:
                    file_hash = hashlib.sha256(file_content).hexdigest()
                    cached_result = self.redis_cache_client.get(file_hash)
                    if cached_result:
                        self.logger.info(f"Cache HIT for file hash: {file_hash}.")
                        result_data = json.loads(cached_result)
                        return self.wrap_response(result_data, msg="Success (from cache)")
                except Exception as e:
                    self.logger.error(f"Redis cache check failed: {e}", exc_info=True)

            if not self.queue_manager.has_active_queues:
                return self.wrap_response({}, -1, "No active workers available.")

            try:
                target_queue = self.queue_manager.get_next_queue()
                self.logger.info(f"Cache MISS. Routing task to queue: {target_queue}")

                suffix = file.filename.split(".")[-1] if '.' in file.filename else 'audio'
                object_name = f"{str(uuid.uuid4())}.{suffix}"
                
                self.minio_client.put_object(
                    self.minio_bucket, object_name, BytesIO(file_content),
                    length=len(file_content), content_type=file.content_type
                )
                self.logger.info(f"File '{object_name}' uploaded. Submitting task...")

                task = self.celery_app.send_task(
                    'asr_worker.asr_task', args=[self.minio_bucket, object_name],
                    expires=60, time_limit=300, queue=target_queue
                )
                self.logger.info(f"Task {task.id} submitted. Waiting for result...")

                result = await self.get_task_result_async(task, timeout=60)
                self.logger.info(f"Task {task.id} completed.")
                
                if self.redis_cache_client and file_hash and result.get("status", -1) == 0:
                    try:
                        data_to_cache = result.get("data", {})
                        self.redis_cache_client.set(
                            file_hash, json.dumps(data_to_cache), ex=self.cache_expiration_seconds
                        )
                        self.logger.info(f"Result for file hash {file_hash} stored in cache.")
                    except Exception as e:
                        self.logger.error(f"Failed to store result in Redis cache: {e}", exc_info=True)

                return self.wrap_response(result.get("data", {}), result.get("status", 0), result.get("message", "success"))
            
            except TimeoutError:
                self.logger.error(f"Task {task.id} timed out.")
                return self.wrap_response({}, -1, "Processing timed out.")
            except S3Error as e:
                self.logger.error(f"MinIO S3 Error: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to upload file to storage.")
            except Exception as e:
                self.logger.error(f"Error processing sync request: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to submit recognition task.")

        @self.app.post("/llm/asr/recognition")
        async def async_recognition(file: UploadFile = File(..., description="audio file")):
            if not self.queue_manager.has_active_queues:
                return self.wrap_response({}, -1, "No active workers available.")

            try:
                target_queue = self.queue_manager.get_next_queue()
                task_id = str(uuid.uuid4())
                suffix = file.filename.split(".")[-1]
                object_name = f"{task_id}.{suffix}"
                self.logger.info(f"ASYNC request. Routing to queue '{target_queue}'.")

                file_content = await file.read()
                self.minio_client.put_object(
                    self.minio_bucket, object_name, BytesIO(file_content),
                    length=len(file_content), content_type=file.content_type
                )
                self.logger.info(f"File '{object_name}' uploaded.")

                self.celery_app.send_task(
                    'asr_worker.asr_task', args=[self.minio_bucket, object_name], task_id=task_id,
                    expires=60, time_limit=300, queue=target_queue
                )
                self.logger.info(f"Task {task_id} sent to queue '{target_queue}'.")

                return self.wrap_response(data_dict={"task_id": task_id}, msg="Task received.")
            
            except S3Error as e:
                self.logger.error(f"MinIO S3 Error: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to upload file to storage.")
            except Exception as e:
                self.logger.error(f"Error processing async request: {e}", exc_info=True)
                return self.wrap_response({}, -1, "Failed to submit recognition task.")

        @self.app.post("/llm/asr/fetch_result")
        async def get_result(task_id: str = Body(..., embed=True)):
            # Note: ASR does not write to cache on result fetch, only on sync completion.
            # This implementation is simpler and just fetches the result.
            return await self._get_result_implementation(task_id)


app = AsrApiServer().app

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)

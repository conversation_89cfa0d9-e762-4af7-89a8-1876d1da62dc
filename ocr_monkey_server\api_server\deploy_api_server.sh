#!/bin/bash
# This script builds the API Server image and then starts the container.
# It links to existing 'rabbitmq-server', 'redis', and 'minio' containers
# by being on the same Docker network.
set -e
IMAGE_NAME="ocr-monkey-server-img"
# --- 1. Build Docker Image ---
echo "Building '${IMAGE_NAME}' from the current directory..."
docker build -t ${IMAGE_NAME} -f Dockerfile.api .
echo "Image '${IMAGE_NAME}' built successfully."
echo ""

# --- 2. Configuration ---
CONTAINER_NAME="ocr-monkey-api-server-container"

HOST_PORT="50103"
CONTAINER_PORT="50103"

GUNICORN_WORKERS=4
source common_lib/server_common.env
echo "load common.env successfully."
echo "RABBITMQ_HOST=${RABBITMQ_HOST}"
echo "RABBITMQ_USER=${RABBITMQ_USER}"
echo "RABBITMQ_PASS=${RABBITMQ_PASS}"
echo "RABBITMQ_PORT=${RABBITMQ_PORT}"
echo "MINIO_HOST=${MINIO_HOST}"
echo "MINIO_PORT=${MINIO_PORT}"
echo "MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}"
echo "MINIO_SECRET_KEY=${MINIO_SECRET_KEY}"
echo "REDIS_HOST=${REDIS_HOST}"
echo "REDIS_PORT=${REDIS_PORT}"
echo "REDIS_CACHE_DB=${OCR_REDIS_CACHE_DB}"
echo "REDIS_CACHE_RESULT=${OCR_REDIS_CACHE_RESULT}"
echo "OCR_RABBITMQ_VHOST=${OCR_RABBITMQ_VHOST}"


# --- 3. Start Container ---
echo "Starting API Server container ('${CONTAINER_NAME}')..."

# Stop and remove the container if it already exists.
if [ "$(docker ps -a -q -f name=${CONTAINER_NAME})" ]; then
    echo "Stopping and removing existing '${CONTAINER_NAME}'..."
    docker stop ${CONTAINER_NAME} >/dev/null
    docker rm ${CONTAINER_NAME} >/dev/null
fi

echo "Attempting to link to '${RABBITMQ_HOST}', '${REDIS_HOST}', and '${MINIO_HOST}'..."

GUNICORN_PATH="/usr/local/bin/gunicorn"

# This version assumes all services are on a shared Docker network (e.g., 'micro_service_network').
# This is the recommended setup for containerized communication.
docker run -d \
  --name ${CONTAINER_NAME} \
  -p ${HOST_PORT}:${CONTAINER_PORT} \
  -e "MINIO_ENDPOINT=${MINIO_HOST}:${MINIO_PORT}" \
  -e "MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}" \
  -e "MINIO_SECRET_KEY=${MINIO_SECRET_KEY}" \
  -e "REDIS_HOST=${REDIS_HOST}" \
  -e "REDIS_PORT=${REDIS_PORT}" \
  -e "REDIS_CACHE_DB=${OCR_REDIS_CACHE_DB}" \
  -e "REDIS_CACHE_RESULT=${OCR_REDIS_CACHE_RESULT}" \
  -e "RABBITMQ_HOST=${RABBITMQ_HOST}" \
  -e "RABBITMQ_USER=${RABBITMQ_USER}" \
  -e "RABBITMQ_PASS=${RABBITMQ_PASS}" \
  -e "RABBITMQ_VHOST=${OCR_RABBITMQ_VHOST}" \
  -e "RABBITMQ_PORT=${RABBITMQ_PORT}" \
  --restart always \
  ${IMAGE_NAME} \
  ${GUNICORN_PATH} api_server:app --workers ${GUNICORN_WORKERS} --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:${CONTAINER_PORT}

echo ""
echo "'${CONTAINER_NAME}' started successfully."
echo "To check logs, run: docker logs ${CONTAINER_NAME}"
echo "Access the API documentation at: http://localhost:${HOST_PORT}/docs"
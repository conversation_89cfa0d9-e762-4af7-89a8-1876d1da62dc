# 在H800服务器上执行
# 1. taskset -c 0-23
# taskset 是 Linux 系统工具，用于设置进程的 CPU 亲和力（CPU Affinity），即限制进程只能在指定的 CPU 核心上运行。

# -c 选项表示后续参数是 CPU 核心的编号列表（而非掩码）。
# 0-23 表示允许 Worker 进程使用 CPU 核心 0 到 23（共 24 个核心）。
# 作用：强制 Worker 只在这些核心上运行，避免与其他进程竞争 CPU 资源，或针对特定硬件优化（如 NUMA 架构）。
# 2. celery -A celery_app worker
# 这是 Celery 的核心命令，用于启动 Worker 进程：

# -A celery_app：指定 Celery 应用的入口模块（即包含 Celery 实例的 Python 模块，例如 celery_app.py）。Worker 会从这个模块中加载任务定义。
# worker：表示启动一个 Worker 进程，负责从消息队列中获取并执行任务。
# 3. Worker 关键参数
# --pool=prefork
# 指定 Worker 使用的进程池类型。prefork 是 Celery 默认的进程池（基于 Unix 的 fork() 机制），通过 多进程模型 处理任务。

# 适用场景：适合 CPU 密集型任务（如大量计算），因为多进程可绕过 Python GIL（全局解释器锁）的限制，充分利用多核 CPU。
# --concurrency=8
# 设置 Worker 的 并发数，即同时处理任务的最大进程数（对 prefork 池而言，等同于子进程数量）。

# 此处设置为 8，表示 Worker 会创建 8 个子进程，每个子进程独立处理一个任务。
# --prefetch-multiplier=1
# 控制 Worker 从消息队列中 预取任务的数量。计算公式为：
# 预取总数 = prefetch-multiplier × concurrency

# 此处 prefetch-multiplier=1，concurrency=8，因此预取总数为 1×8=8 个任务。
# 每个子进程一次仅预取 1 个任务（处理完再取下一个）。这种设置可避免任务堆积在单个进程中，使任务更均匀地分配给所有子进程（尤其适用于任务执行时间差异较大的场景）。
# --loglevel=info
# 设置日志输出级别为 info（信息级）。

# 常见级别：debug（最详细）→ info（常规信息）→ warning（警告）→ error（错误）→ critical（严重错误）。
# 此处选择 info 级别，输出任务执行的关键状态（如任务开始、完成、失败等），但不会过于冗长。
# -Q gpu0
# 指定 Worker 监听的 任务队列 为 gpu0。

# Celery 支持多队列路由，默认队列是 celery。通过 -Q 选项，Worker 仅处理发送到 gpu0 队列的任务，其他队列的任务会被忽略。
# 典型场景：将 GPU 相关任务（如深度学习训练）路由到 gpu0 队列，由专用 Worker 处理，与 CPU 密集型任务解耦。
# 总结
# 这条命令启动了一个 绑定 CPU 核心 0-23、使用多进程池、并发数 8、预取策略均衡、专注处理 gpu0 队列任务 的 Celery Worker。适用于需要隔离资源（如 GPU 计算）、优化任务分配的场景（例如 AI 训练任务集群）。




export CUDA_VISIBLE_DEVICES=7

# 这条命令启动了一个 绑定 CPU 核心 0-23、使用多进程池、并发数 8、预取策略均衡、专注处理 gpu0 队列任务 的 Celery Worker。适用于需要隔离资源（如 GPU 计算）、优化任务分配的场景（例如 AI 训练任务集群）
taskset -c 0-23 celery -A celery_app worker \
        --pool=prefork \
        --concurrency=8 \
        --prefetch-multiplier=1 \
        --loglevel=info \
        # -Q gpu0
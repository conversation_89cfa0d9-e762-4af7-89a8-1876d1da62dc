

import os
from locust import HttpUser, task, between
TEST_AUDIO_PATH = "potry.mp3"
API_ENDPOINT = "https://matrix.nibs.ac.cn/aiservices/llm/asr/recognition"
    
class ASRUser(HttpUser):
    """
    This Locust user simulates a client uploading an audio file
    to the asynchronous ASR endpoint.
    """
    wait_time = between(0.1, 0.5)  # Time between consecutive tasks
    
    def on_start(self):
        """
        This method is called when a virtual user starts.
        It reads the audio file from disk into memory.
        """
        if not os.path.exists(TEST_AUDIO_PATH):
            print(f"Error: Test audio file not found at '{TEST_AUDIO_PATH}'")
            # Stop the test if the file doesn't exist
            self.environment.runner.quit()

        with open(TEST_AUDIO_PATH, "rb") as f:
            self.audio_file_content = f.read()
        print(f"Successfully loaded test audio file: {TEST_AUDIO_PATH}")
    
    @task
    def recognize_audio(self):
        """
        This task simulates a single API request.
        It sends the pre-loaded audio file as a multipart/form-data upload.
        """
        if not hasattr(self, 'audio_file_content'):
            # This can happen if on_start failed.
            return

        # The 'files' parameter tells requests (used by Locust)
        # to send a multipart/form-data request.
        # The key 'file' must match the parameter name in your FastAPI endpoint.
        files = {
            "file": (TEST_AUDIO_PATH, self.audio_file_content, "audio/mpeg")
        }

        self.client.post(
            API_ENDPOINT,
            files=files
        )
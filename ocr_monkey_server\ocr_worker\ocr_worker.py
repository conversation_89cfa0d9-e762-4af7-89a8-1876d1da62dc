#!/usr/bin/env python3
"""
OCR Worker using MonkeyOCR model with Celery
"""

import os
import sys
import time
import tempfile
import base64
from io import BytesIO
from celery import Task
from celery.signals import worker_process_init
from minio import Minio
from minio.error import S3Error
import logging

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from celery_app import celery_app
from magic_pdf.model.custom_model import MonkeyOCR
from parse import single_task_recognition, parse_file

# --- Logging Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- MinIO Configuration ---
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "minio:9000")
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin")

minio_client = Minio(
    MINIO_ENDPOINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=False
)

# --- Custom Task Class for OCR ---
class OCRTask(Task):
    """
    A custom Celery Task class that holds the loaded MonkeyOCR model.
    The model is loaded once per worker process and attached to this class.
    """
    _model = None

    @property
    def model(self):
        """
        Property to access the model. Ensures the model is loaded.
        The linter can understand this direct access pattern.
        """
        # The model should have been loaded by the signal handler.
        # This property access makes the dependency explicit.
        return self._model

    @classmethod
    def set_model(cls, model):
        """Class method to set the loaded model from the signal handler."""
        cls._model = model

# --- Signal Handler to Load Model ---
@worker_process_init.connect
def init_model_handler(**kwargs):
    """
    This function is called when a Celery worker process starts.
    It loads the MonkeyOCR model and attaches it to the OCRTask class.
    """
    logger.info("Worker process initializing... Loading MonkeyOCR model.")
    
    try:
        # Get model configuration path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(current_dir, "model_configs.yaml")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Model config file not found: {config_path}")
        
        # Load MonkeyOCR model
        loaded_model = MonkeyOCR(config_path)
        
        # Attach the loaded model to our custom task class
        OCRTask.set_model(loaded_model)
        logger.info("MonkeyOCR model loaded and attached to OCRTask successfully.")
        
    except Exception as e:
        logger.error(f"Failed to load MonkeyOCR model: {e}", exc_info=True)
        raise

# --- Celery Task Definition ---
@celery_app.task(bind=True, base=OCRTask, name='ocr_worker.ocr_task')
def ocr_task(self: OCRTask, bucket_name: str, object_name: str, split_pages: bool = False, return_type: str = "text"):
    """
    Celery task for OCR processing. `self` is an instance of OCRTask.
    
    Args:
        bucket_name: MinIO bucket name
        object_name: Object name in MinIO
        split_pages: Whether to split pages for PDF processing
        return_type: Type of processing ('markdown', 'text', 'formula', 'table')
    """
    task_id = self.request.id
    queue_name = self.request.delivery_info['routing_key']
    logger.info(f"[{task_id}] Received task from queue '{queue_name}': process {object_name} from bucket {bucket_name}")
    logger.info(f"[{task_id}] Parameters: split_pages={split_pages}, return_type={return_type}")

    try:
        # 1. Download file from MinIO
        response = minio_client.get_object(bucket_name, object_name)
        file_data = response.read()
        response.close()
        response.release_conn()
        
        logger.info(f"[{task_id}] Downloaded file {object_name} ({len(file_data)} bytes)")

        # 2. Save file temporarily
        file_extension = object_name.split(".")[-1].lower()
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_extension}") as temp_file:
            temp_file.write(file_data)
            temp_file_path = temp_file.name

        try:
            # 3. Create output directory
            output_dir = tempfile.mkdtemp(prefix=f"ocr_{return_type}_")
            
            # 4. Run OCR inference using the model from the task class
            start_time = time.time()
            
            if return_type in ['text', 'formula', 'table']:
                # Single task recognition
                result_dir = single_task_recognition(
                    temp_file_path,
                    output_dir,
                    self.model,
                    return_type
                )
                
                # Read result file
                result_files = [f for f in os.listdir(result_dir) if f.endswith(f'_{return_type}_result.md')]
                if not result_files:
                    raise Exception("No result file generated")
                
                result_file_path = os.path.join(result_dir, result_files[0])
                with open(result_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                end_time = time.time()
                logger.info(f"[{task_id}] OCR {return_type} recognition finished in {end_time - start_time:.2f}s.")
                
                return {
                    "status": 0,
                    "msg": "success",
                    "data": {
                        "result": content,
                        "infer_time": round(end_time - start_time, 2)
                    }
                }
                
            else:
                # Full document parsing (markdown)
                result_dir = parse_file(
                    temp_file_path,
                    output_dir,
                    self.model,
                    split_pages
                )
                
                end_time = time.time()
                logger.info(f"[{task_id}] OCR document parsing finished in {end_time - start_time:.2f}s.")
                
                # Create ZIP file with results
                import zipfile
                zip_filename = f"ocr_result_{task_id}.zip"
                zip_path = os.path.join(result_dir, zip_filename)
                
                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(result_dir):
                        for file in files:
                            if file != zip_filename:  # Don't include the zip file itself
                                file_path = os.path.join(root, file)
                                rel_path = os.path.relpath(file_path, result_dir)
                                zipf.write(file_path, rel_path)
                
                # Convert ZIP to base64
                with open(zip_path, "rb") as f:
                    zip_content = f.read()
                zip_base64 = base64.b64encode(zip_content).decode("utf-8")
                
                return {
                    "status": 0,
                    "msg": "success",
                    "data": {
                        "zip_base64": zip_base64,
                        "infer_time": round(end_time - start_time, 2)
                    }
                }
                
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except S3Error as e:
        logger.error(f"[{task_id}] MinIO S3 Error: {e}", exc_info=True)
        return {"status": 2, "msg": f"Failed to download file from storage: {str(e)}", "data": {}}
    except Exception as e:
        logger.error(f"[{task_id}] An unexpected error occurred: {e}", exc_info=True)
        return {"status": 1, "msg": f"OCR processing failed: {str(e)}", "data": {}}

import os
import asyncio
import json
from contextlib import asynccontextmanager
from celery.result import AsyncResult
from fastapi import FastAP<PERSON>, Body
from fastapi.responses import JSONResponse
from minio import Minio
import redis
import logging
import time
from logging.handlers import TimedRotatingFileHandler

from common_lib.celery_manager import CeleryQueueManager

class BaseApiServer:
    """
    A base class for creating FastAPI servers for AI microservices,
    handling common configurations and endpoints.
    """
    def __init__(self, service_name: str, celery_app, minio_bucket: str, log_filename: str,redis_cache_db: int = -1):
        # --- Core Attributes ---
        self.service_name = service_name
        self.celery_app = celery_app
        self.minio_bucket = minio_bucket
        self.redis_cache_db = redis_cache_db
        
        # --- Logger ---
        self.logger = self.setup_timed_rotating_logger(log_filename)

        # --- Common Configurations ---
        self._load_common_configs()

        # --- Client Initializations ---
        self.minio_client = self._init_minio_client()
        self.redis_cache_client = None
        if self.redis_cache_db != -1:
            self.redis_cache_client = self._init_redis_cache_client()
        self.queue_manager = CeleryQueueManager(self.celery_app)

        # --- FastAPI App ---
        self.app = FastAPI(
            title=f"{self.service_name} API Gateway",
            lifespan=self.lifespan
        )
        self._register_common_routes()

    def _load_common_configs(self):
        """Loads configuration from environment variables."""
        self.minio_endpoint = os.getenv("MINIO_ENDPOINT", "localhost:9000").strip()
        self.minio_access_key = os.getenv("MINIO_ACCESS_KEY", "admin").strip()
        self.minio_secret_key = os.getenv("MINIO_SECRET_KEY", "minioadmin").strip()
        self.redis_host = os.getenv("REDIS_HOST", "**************").strip()
        self.redis_port = int(os.getenv("REDIS_PORT", "6379").strip())
        self.cache_expiration_seconds = 86400  # 24 hours

    def _init_minio_client(self) -> Minio:
        """Initializes and returns the MinIO client."""
        return Minio(
            self.minio_endpoint,
            access_key=self.minio_access_key,
            secret_key=self.minio_secret_key,
            secure=False
        )

    def _init_redis_cache_client(self):
        """Initializes and returns the Redis cache client."""
        try:
            client = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_cache_db,
                decode_responses=True
            )
            client.ping()
            self.logger.info(f"Successfully connected to Redis cache at {self.redis_host}:{self.redis_port}, DB: {self.redis_cache_db}")
            return client
        except redis.exceptions.ConnectionError as e:
            self.logger.error(f"Could not connect to Redis cache: {e}", exc_info=True)
            return None


    def setup_timed_rotating_logger(self, filename: str, level: int = logging.INFO) -> logging.Logger:
        """
        Sets up a timed rotating file logger.
        """
        handler = TimedRotatingFileHandler(
            filename=filename, when="midnight", interval=1, backupCount=15, encoding="utf-8"
        )
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        
        logger = logging.getLogger(filename) # Use filename as logger name for uniqueness
        logger.setLevel(level)
        
        # Avoid adding handlers multiple times
        if not logger.handlers:
            logger.addHandler(handler)
            
        return logger

    def wrap_response(self, data_dict: dict = {}, status: int = 0, msg: str = 'success') -> JSONResponse:
        """
        Wraps a dictionary into a standardized JSON response format.
        """
        return JSONResponse(
            status_code=200,
            content={"data": data_dict, 'status': status, 'msg': msg}
        )

    async def get_task_result_async(self, task: AsyncResult, timeout: int) -> any:
        """
        Asynchronously and non-blockingly polls for a Celery task result.
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            if task.ready():
                return task.get(timeout=1)  # Short timeout, as it should be ready
            await asyncio.sleep(0.05)  # Non-blocking sleep
        raise TimeoutError(f"Task {task.id} did not complete within {timeout} seconds.")
    @asynccontextmanager
    async def lifespan(self, app: FastAPI):
        """Handles application startup and shutdown events."""
        self.logger.info(f"{self.service_name} application startup...")
        self.queue_manager.update_active_queues()
        update_task = self.queue_manager.start_background_updater(60)
        
        self.logger.info(f"Checking for MinIO bucket: '{self.minio_bucket}'")
        try:
            if not self.minio_client.bucket_exists(self.minio_bucket):
                self.minio_client.make_bucket(self.minio_bucket)
                self.logger.info(f"Bucket '{self.minio_bucket}' created in MinIO.")
            else:
                self.logger.info(f"Bucket '{self.minio_bucket}' already exists.")
        except Exception as e:
            self.logger.error(f"Could not connect to MinIO or create bucket: {e}")
        
        yield
        
        self.logger.info(f"{self.service_name} application shutdown...")
        self.queue_manager.cancel_background_updater()
        try:
            await update_task
        except asyncio.CancelledError:
            self.logger.info("Background queue updater task cancelled successfully.")

    def _register_common_routes(self):
        """Registers common endpoints like health checks."""
        @self.app.get(f"/llm/{self.service_name.lower()}/health")
        async def health():
            status = "healthy" if self.queue_manager.has_active_queues else "unhealthy"
            info = {
                "celery_broker": self.celery_app.conf.broker_url,
                "celery_result_backend": self.celery_app.conf.result_backend,
                "minio_bucket": self.minio_bucket,
                "active_queues": self.queue_manager.active_queues_cache,
                "status": status
            }
            return JSONResponse(content={"status": status, "info": info})

    async def _get_result_implementation(self, task_id: str):
        """Provides a common implementation for fetching task results."""
        self.logger.info(f"Querying result for task ID: {task_id}")
        task_result = AsyncResult(task_id, app=self.celery_app)
        self.logger.info(f"Task state: {task_result.state}")

        if task_result.state == 'SUCCESS':
            result = task_result.result
            return result
        
        elif task_result.state == 'PENDING':
            return self.wrap_response({"status": "PENDING"}, msg="Task is waiting or may have expired.")
        elif task_result.state == 'PROGRESS':
            return self.wrap_response({"status": "PROCESSING", "result": task_result.info}, msg="Task is being processed.")
        elif task_result.state == 'FAILURE':
            self.logger.error(f"Task {task_id} failed. Reason: {task_result.info}")
            return self.wrap_response({"status": "FAILURE", "result": str(task_result.info)}, -1, "Task failed.")
        else:
            return self.wrap_response({"status": task_result.state}, msg="Unknown task state.")

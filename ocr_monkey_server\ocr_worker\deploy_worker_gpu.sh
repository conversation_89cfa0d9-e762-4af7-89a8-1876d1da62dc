#!/bin/bash
# This script deploys the ASR Worker for a GPU environment.
# It can launch multiple worker containers on the same machine.
#
# Usage: ./deploy_worker_gpu.sh [NUM_WORKERS] [START_PORT]
# Example (1 worker): ./deploy_worker_gpu.sh
# Example (2 workers, ports from 50303): ./deploy_worker_gpu.sh 2 50303

set -e

# --- Step 1: Configuration ---
MODEL_CACHE_PATH="/data02/zhaokai/micro_service/models/monkey_ocr/l1731396519/MonkeyOCR"
IMAGE_NAME="ocr-monkey-gpu-worker-img:latest"

# Since all services (Red<PERSON>, MinIO, RabbitMQ, Workers) are on the same
# Docker network, we can use their container names as hostnames.
source common_lib/worker_common.env
echo "load worker_common.env successfully."
echo "RABBITMQ_HOST=${RABBITMQ_HOST}"
echo "RABBITMQ_USER=${RABBITMQ_USER}"
echo "RABBITMQ_PASS=${RABBITMQ_PASS}"
echo "RABBITMQ_PORT=${RABBITMQ_PORT}"
echo "RABBITMQ_VHOST=${OCR_RABBITMQ_VHOST}"
echo "REDIS_HOST=${REDIS_HOST}"
echo "REDIS_PORT=${REDIS_PORT}"
echo "REDIS_CACHE_RESULT=${OCR_REDIS_CACHE_RESULT}"
echo "MINIO_HOST=${MINIO_HOST}"
echo "MINIO_PORT=${MINIO_PORT}"
echo "MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}"
echo "MINIO_SECRET_KEY=${MINIO_SECRET_KEY}"

MICRO_SERVICE_NETWORK="micro_service_network"

CPU_CORES=192
GPU_CARD=8
CPU_PER_NPU=$((CPU_CORES / GPU_CARD))


# 定义每个gpu要启动的容器的初始端口，列表的长度就是worker的数量, 类似元组，没有逗号
GPU_LIST=(7)
GPU_START_PORT=(50305)
WORKER_CONCURRENCY=1

echo "--- Building Docker image ('${IMAGE_NAME}') ---"
# Rebuild the image to include the new 'pika' dependency
docker build --network=host -t "${IMAGE_NAME}" -f Dockerfile.worker.gpu .
echo "Image build complete."
echo ""

echo "--- Configuring deployment settings ---"
echo "Number of workers to create: ${NUM_WORKERS}"
echo "Host model path set to: ${MODEL_CACHE_PATH}"
echo "Using image: ${IMAGE_NAME}"
echo ""

echo "--- Step 2: Starting ${NUM_WORKERS} worker container(s) ---"

# 遍历WORKER_GPU_LIST数组
for i in $(seq 0 $((${#GPU_LIST[@]} - 1)))
do
  CPU_START_IDX=$((i * CPU_PER_NPU))
  CPU_END_IDX=$((CPU_START_IDX + CPU_PER_NPU - 1))
  CONTAINER_NAME="ocr-monkey-worker-gpu-${GPU_LIST[$i]}"
  echo "--> Starting worker #${i} with container name: ${CONTAINER_NAME}"

  if [ "$(docker ps -a -q -f name=${CONTAINER_NAME})" ]; then
      echo "    Stopping and removing existing container..."
      docker stop "${CONTAINER_NAME}" >/dev/null
      docker rm "${CONTAINER_NAME}" >/dev/null
  fi

#   HOST_SSH_PORT=$(($START_PORT + $i))
  
#   echo "    Launching new container..."
#   echo "    SSH Port mapping: ${HOST_SSH_PORT}:22"

  TASKSET_CMD="taskset -c $CPU_START_IDX-$CPU_END_IDX celery -A celery_app worker --loglevel=info --concurrency=${WORKER_CONCURRENCY} --pool=prefork --prefetch-multiplier=1 -Q gpu_${GPU_LIST[$i]}"
  echo "    Command: ${TASKSET_CMD}"
  docker run -d \
    --gpus all \
    --name "${CONTAINER_NAME}" \
    --network "${MICRO_SERVICE_NETWORK}" \
    -p ${GPU_START_PORT[$i]}:${GPU_START_PORT[$i]} \
    -v "${MODEL_CACHE_PATH}:/models:ro" \
    -e "MINIO_ENDPOINT=${MINIO_HOST}:${MINIO_PORT}" \
    -e "MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}" \
    -e "MINIO_SECRET_KEY=${MINIO_SECRET_KEY}" \
    -e "REDIS_HOST=${REDIS_HOST}" \
    -e "REDIS_PORT=${REDIS_PORT}" \
    -e "REDIS_CACHE_RESULT=${OCR_REDIS_CACHE_RESULT}" \
    -e "RABBITMQ_USER=${RABBITMQ_USER}" \
    -e "RABBITMQ_PASS=${RABBITMQ_PASS}" \
    -e "RABBITMQ_HOST=${RABBITMQ_HOST}" \
    -e "RABBITMQ_PORT=${RABBITMQ_PORT}" \
    -e "RABBITMQ_VHOST=${OCR_RABBITMQ_VHOST}" \
    --restart always \
    "${IMAGE_NAME}" \
    bash -c "\
    export CUDA_VISIBLE_DEVICES=${GPU_LIST[$i]}
    ${TASKSET_CMD}
    "

    # celery -A celery_app worker --loglevel=info --concurrency=8 --pool=prefork
done

echo ""
echo "--- Deployment Complete ---"
echo "${#GPU_LIST[@]} worker(s) started successfully."
echo "To view logs for a specific worker, run: docker logs -f ${CONTAINER_NAME}"

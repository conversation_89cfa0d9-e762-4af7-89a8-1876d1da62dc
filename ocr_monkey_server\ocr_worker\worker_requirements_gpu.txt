# Requirements for the ASR worker application logic.
# The base image already provides funasr, torch, etc.
celery
redis
minio
ffmpeg-python
# Add the driver for RabbitMQ
pika

accelerate==1.8.1
addict==2.4.0
aiofiles==23.2.1
aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
airportsdata==20250622
albucore==0.0.24
albumentations==2.0.8
annotated-types==0.7.0
anyio==4.9.0
astor==0.8.1
async-timeout==5.0.1
attrs==25.3.0
av==14.4.0
blake3==1.0.5
boto3==1.38.45
botocore==1.38.45
Brotli==1.1.0
cachetools==6.1.0
certifi==2025.6.15
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
cloudpickle==3.1.1
colorlog==6.9.0
compressed-tensors==0.10.1
contourpy==1.3.2
cryptography==45.0.4
cupy-cuda12x==13.4.1
cycler==0.12.1
depyf==0.18.0
dill==0.4.0
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
doclayout_yolo==0.0.2b1
einops==0.8.1
email_validator==2.2.0
exceptiongroup==1.3.0
fast-langdetect==0.3.2
fastapi==0.115.14
fastapi-cli==0.0.7
fastrlock==0.8.3
fasttext-predict==0.9.2.4
ffmpy==0.6.0
filelock==3.18.0
fire==0.7.0
fonttools==4.58.4
frozenlist==1.7.0
fsspec==2025.5.1
gguf==0.17.1
googleapis-common-protos==1.70.0
gradio==5.23.3
gradio_client==1.8.0
groovy==0.1.2
grpcio==1.73.1
gunicorn==23.0.0
h11==0.16.0
hf-xet==1.1.5
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.33.1
idna==3.10
importlib_metadata==8.7.0
interegular==0.3.3
Jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.1
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
lark==1.2.2
llguidance==0.7.30
llvmlite==0.44.0
lm-format-enforcer==0.10.11
lmdeploy==0.8.0
loguru==0.7.3
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.3
mdurl==0.1.2
mistral_common==1.6.2
mmengine-lite==0.10.7
modelscope==1.27.1
mpmath==1.3.0
msgpack==1.1.1
msgspec==0.19.0
multidict==6.5.1
nest-asyncio==1.6.0
networkx==3.4.2
ninja==1.11.1.4
numba==0.61.2
numpy==1.26.4
nvidia-cublas-cu12==12.4.5.8
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==9.1.0.70
nvidia-cufft-cu12==11.2.1.3
nvidia-cufile-cu12==1.11.1.6
nvidia-curand-cu12==10.3.5.147
nvidia-cusolver-cu12==11.6.1.9
nvidia-cusparse-cu12==12.3.1.170
nvidia-cusparselt-cu12==0.6.2
nvidia-ml-py==12.575.51
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
openai==1.88.0
opencv-python==4.11.0.86
opencv-python-headless==4.11.0.86
opentelemetry-api==1.34.1
opentelemetry-exporter-otlp==1.34.1
opentelemetry-exporter-otlp-proto-common==1.34.1
opentelemetry-exporter-otlp-proto-grpc==1.34.1
opentelemetry-exporter-otlp-proto-http==1.34.1
opentelemetry-proto==1.34.1
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
opentelemetry-semantic-conventions-ai==0.4.9
orjson==3.10.18
outlines==0.1.11
outlines_core==0.1.26
packaging==25.0
pandas==2.3.0
partial-json-parser==*******.post6
pdf2image==1.17.0
pdfminer.six==20231228
peft==0.14.0
pillow==11.2.1
platformdirs==4.3.8
prometheus-fastapi-instrumentator==7.1.0
prometheus_client==0.22.1
propcache==0.3.2
protobuf==5.29.5
psutil==7.0.0
py-cpuinfo==9.0.0
pycocotools==2.0.10
pycountry==24.6.1
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
pydub==0.25.1
Pygments==2.19.2
PyMuPDF==1.24.14
pynvml==12.0.0
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-json-logger==3.3.0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
pyzmq==27.0.0
qwen-vl-utils==0.0.10
ray==2.47.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
rich==14.0.0
rich-toolkit==0.14.7
robust-downloader==0.0.2
rpds-py==0.25.1
ruff==0.12.1
s3transfer==0.13.0
safehttpx==0.1.6
safetensors==0.5.3
scikit-learn==1.7.0
scipy==1.15.3
seaborn==0.13.2
semantic-version==2.10.0
sentencepiece==0.2.0
shellingham==1.5.4
shortuuid==1.0.13
simsimd==6.4.9
six==1.17.0
sniffio==1.3.1
starlette==0.46.2
stringzilla==3.12.5
sympy==1.13.1
termcolor==3.1.0
thop==0.1.1.post2209072238
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.2
tomli==2.2.1
tomlkit==0.13.3
torch==2.6.0
torchaudio==2.7.0
torchvision==0.21.0
tqdm==4.67.1
transformers==4.52.4
triton==3.2.0
typer==0.16.0
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
urllib3==2.5.0
uv==0.7.15
uvicorn==0.34.3
uvloop==0.21.0
vllm==0.9.1
watchfiles==1.1.0
websockets==15.0.1
xformers==0.0.30
xgrammar==0.1.19
yapf==0.43.0
yarl==1.20.1
zipp==3.23.0
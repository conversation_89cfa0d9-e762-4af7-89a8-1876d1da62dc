# Requirements for the ASR worker application logic.
# The base image already provides funasr, torch, etc.
celery
redis
minio
ffmpeg-python
# Add the driver for RabbitMQ
pika

boto3>=1.28.43
Brotli>=1.1.0
click>=8.1.7
fast-langdetect>=0.2.3
loguru>=0.6.0
numpy>=1.21.6,<2.0.0
pydantic>=2.7.2
PyMuPDF>=1.24.9,<=1.24.14
scikit-learn>=1.0.2
pdfminer.six==20231228
pycocotools>=2.0.6
transformers==4.52.4
qwen_vl_utils==0.0.10
matplotlib
doclayout_yolo==0.0.2b1
PyYAML
dill>=0.3.8,<1
pdf2image==1.17.0
openai==1.88.0
from celery import Celery
import os

# --- Broker Configuration (RabbitMQ) ---
RABBITMQ_USER = os.getenv("RABBITMQ_USER", "rabbitadmin").strip()
RABBITMQ_PASS = os.getenv("RABBITMQ_PASS", "rabbitadmin").strip()
RABBITMQ_HOST = os.getenv("RABBITMQ_HOST", "rabbitmq").strip() # Use the container name
RABBITMQ_PORT = os.getenv("RABBITMQ_PORT", "5672").strip()
RABBITMQ_VHOST = os.getenv("RABBITMQ_VHOST", "tts_vhost").strip()
BROKER_URL = f"amqp://{RABBITMQ_USER}:{RABBITMQ_PASS}@{RABBITMQ_HOST}:{RABBITMQ_PORT}/{RABBITMQ_VHOST}"

# --- Result Backend Configuration (Redis) ---
REDIS_HOST = os.getenv("REDIS_HOST", "redis").strip()
REDIS_PORT = os.getenv("REDIS_PORT", "6379").strip()
REDIS_CACHE_RESULT = os.getenv("REDIS_CACHE_RESULT", "1").strip()
RESULT_BACKEND_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_CACHE_RESULT}"

celery_app = Celery(
    'tts_tasks',
    broker=BROKER_URL,
    backend=RESULT_BACKEND_URL,
)

celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    result_expires=3600,
    broker_connection_retry_on_startup=True,
    # --- Redis Transport Options for Robustness ---
    result_backend_transport_options={
        'max_connections': 128,
        'socket_connect_timeout': 10,
        'socket_timeout': 15,  # Add socket-level timeout
        'socket_keepalive': True,
        'health_check_interval': 30,
    }
)

# Explicitly import the tasks module after the app is defined and configured.
import tts_worker

# Step 1: Start from a modern, official NVIDIA CUDA base image
# This version is chosen to be close to the existing images on the server to speed up the build.
# FROM nvidia/cuda:12.2.2-cudnn8-devel-ubuntu22.04
# 已经内置了python 3.10 git lsof torch vim ffmpeg
FROM cuda_12.2.2-cudnn8-devel-ubuntu22.04_torch_2.5.1:latest

# Step 2: Set environment variables to prevent interactive prompts during installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai


# Step 5: Install TTSIndex and other Python dependencies
# Copy requirements first to leverage Docker layer caching
WORKDIR /workspace/tts_index/tts_worker
COPY worker_requirements_gpu.txt .
RUN pip install -r worker_requirements_gpu.txt -i https://mirrors.aliyun.com/pypi/simple/

# Step 6: Copy your application code into the image
COPY . .

# Clear the entrypoint from the base image so our command becomes PID 1
ENTRYPOINT []


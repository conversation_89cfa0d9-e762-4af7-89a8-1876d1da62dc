# Step 1: Start from a modern, official NVIDIA CUDA base image
# This version is chosen to be close to the existing images on the server to speed up the build.
FROM nvidia/cuda:12.2.2-cudnn8-devel-ubuntu22.04

# Step 2: Set environment variables to prevent interactive prompts during installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# Step 3: Install essential system dependencies
# - git: for funasr to pull dependencies
# - ffmpeg: for audio conversion
# - python3 and pip: for our application
# - python3.8-dev: provides header files needed to compile some pip packages
RUN apt-get update && apt-get install -y \
    git \
    ffmpeg \
    python3.10 \
    python3.10-dev \
    python3.10-venv \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Step 4: Install a compatible PyTorch version first
# This version is built for CUDA 12.1 and is compatible with H800 GPUs and many others.
# We use cu121 as it's a standard and broadly compatible choice.
RUN pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Step 5: Install FunASR and other Python dependencies
# Copy requirements first to leverage Docker layer caching
WORKDIR /workspace/fun_asr/asr_worker
COPY worker_requirements_gpu.txt .
RUN pip install -U funasr -i https://mirrors.aliyun.com/pypi/simple/
RUN pip install -r worker_requirements_gpu.txt -i https://mirrors.aliyun.com/pypi/simple/

# Step 6: Copy your application code into the image
COPY asr_worker.py .
COPY celery_app.py .
COPY hotwords.txt .

# Clear the entrypoint from the base image so our command becomes PID 1
ENTRYPOINT []


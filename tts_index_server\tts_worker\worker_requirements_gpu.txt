# Requirements for the ASR worker application logic.
# The base image already provides funasr, torch, etc.
celery
redis
minio
ffmpeg-python
# Add the driver for RabbitMQ
pika
accelerate==0.25.0
transformers==4.36.2
tokenizers==0.15.0
cn2an==0.5.22
ffmpeg-python==0.2.0
Cython==3.0.7
g2p-en==2.1.0
jieba==0.42.1
keras==2.9.0
numba==0.58.1
numpy==1.26.2
pandas==2.1.3
matplotlib==3.8.2
opencv-python==4.9.0.80
vocos==0.1.0
tensorboard==2.9.1
omegaconf
sentencepiece
librosa
tqdm

WeTextProcessing; platform_machine != "Darwin"
wetext; platform_system == "Darwin"
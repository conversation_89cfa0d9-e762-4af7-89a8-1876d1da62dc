# ASR FunASR 服务性能调优与压力测试指南

## 1. 目标

本文档旨在提供一个清晰、可重复的流程，用于对部署在 8 卡 H800 服务器上的 FunASR Celery 服务进行性能调优和压力测试。

**最终目标：** 在满足可接受的延迟（例如，95%的请求在500毫秒内完成）的前提下，找到服务器能支持的**最大并发路数**，并确定最佳的 Celery Worker 配置。

## 2. 核心概念

我们的性能瓶颈通常在两个地方：**CPU预处理**和**GPU推理**。调优的本质就是让这两者的处理能力达到平衡。

*   **`--concurrency` (Celery Worker)**:
    *   **作用**: 控制一个 Worker 进程能**并行处理多少个CPU密集型任务**（如音频下载、ffmpeg解码、数据准备）。
    *   **影响**: 这个值决定了我们能多快地“喂”数据给GPU。如果太低，GPU会“饿死”；如果太高，CPU会过载，导致任务排队和延迟增加。

*   **`max_batch_size` (FunASR模型)**:
    *   **作用**: 控制一次送入GPU进行推理的**最大音频流数量**。
    *   **影响**: 这是提升GPU效率的关键。GPU一次处理16个音频流的耗时远小于分别处理16次的耗时总和。我们已经在代码中将其硬编码为`16`，这是一个适合短音频场景的良好起点。

**调优的核心任务：** 找到最佳的 `--concurrency` 值，使得CPU的处理速度刚好能跟上GPU的批处理（Batching）消耗速度。

## 3. 准备工作

1.  **构建最终镜像**: 确保你已经使用最终的代码构建了 `funasr-worker-gpu-image:latest` 镜像。
2.  **部署基础服务**:
    *   启动 Redis 和 MinIO 服务。
    *   启动 `api_server` 服务，并确保它已配置为可以将任务路由到所有8个队列（`gpu0`, `gpu1`, ..., `gpu7`）。
3.  **安装压测工具**: 在一台独立的机器上（不要在H800服务器本身）安装一个压测工具。推荐使用 Python 的 `locust`。
    ```bash
    pip install locust
    ```
4.  **准备测试音频**: 在 MinIO 中上传一个典型的测试音频文件（例如，10秒左右的 `.mp3`），并记下它的 `bucket_name` 和 `object_name`。
5.  **准备监控终端**: 打开多个SSH终端连接到H800服务器，准备好以下监控命令：
    *   **GPU监控**: `watch -n 1 nvidia-smi`
    *   **CPU监控**: `htop`
    *   **容器监控**: `docker stats`

## 4. 调优步骤

### 第1步：单 Worker 基准测试

我们的目标是先榨干**单张H800卡**的性能，找到最优的单卡配置，然后再扩展到8张卡。

1.  **启动单个Worker**:
    *   只启动一个 Worker 容器，并将其绑定到 GPU 0 和第一组CPU核心上。
    *   从一个**保守的并发值**开始，例如 `--concurrency=8`。

    ```bash
    # 在H800服务器上执行
    export CUDA_VISIBLE_DEVICES=0
    taskset -c 0-23 celery -A celery_app worker \
        --pool=prefork \
        --concurrency=8 \
        --prefetch-multiplier=1 \
        --loglevel=info \
        -Q gpu0
    ```

2.  **编写 `locustfile.py`**:
    *   在你的压测机上，创建一个名为 `locustfile.py` 的文件。你需要将 `YOUR_API_SERVER_IP`, `bucket_name`, 和 `object_name` 替换为你的实际值。

    ```python
    import time
    from locust import HttpUser, task, between

    class ASRUser(HttpUser):
        wait_time = between(0.1, 0.5)  # 模拟用户思考时间

        @task
        def asr_task(self):
            # 替换为你的API服务器地址和测试文件信息
            api_endpoint = "/aiservices/llm/asr/recognition" 
            payload = {
                "bucket_name": "audio-bucket",
                "object_name": "test_10s.mp3"
            }
            self.client.post(api_endpoint, json=payload)

    ```

3.  **运行初步压测**:
    *   启动 Locust，并将并发用户数设置为你刚才配置的 `concurrency` 值（这里是8个用户）。
    *   `locust -f locustfile.py --host=http://YOUR_API_SERVER_IP:PORT`
    *   打开浏览器访问 `http://<your-locust-machine-ip>:8089`，输入并发用户数为 `8`，Ramp-up rate 为 `2`，然后开始测试。

4.  **观察和记录**:
    *   在 Locust Web UI 中，观察 **RPS (Requests Per Second)** 和 **响应时间 (Response Times)**。
    *   在 `nvidia-smi` 终端中，观察 GPU 0 的 **GPU-Util** 和 **显存（Memory）** 使用情况。
    *   在 `htop` 终端中，观察 CPU 0-23 的**使用率**。
    *   让测试运行几分钟，记录下稳定后的各项指标。

### 第2步：寻找最佳单 Worker 并发数

现在我们逐步增加并发数，找到性能拐点。

1.  **停止旧Worker** (`Ctrl+C`)。
2.  **增加并发数**: 将 `--concurrency` 从 `8` 增加到 `12`，然后重新启动 Worker。
3.  **重新压测**: 在 Locust 中，使用 `12` 个并发用户重新进行测试。
4.  **观察和记录** 新的 RPS、延迟和资源利用率。
5.  **重复此过程**:
    *   不断增加 `--concurrency` 的值（例如 `16`, `20`, `24`, `32`...）。
    *   持续压测并记录结果。

你会观察到以下趋势：
*   一开始，随着 `--concurrency` 增加，RPS会显著提高，而延迟保持稳定。
*   到达一个点后，再增加 `--concurrency`，RPS提升会变得不明显，但**平均响应时间和95%响应时间会开始显著增加**。
*   同时，你会看到分配给这个Worker的CPU核心（0-23）会持续处于高位（接近100%）。

**这个点就是这组CPU核心能支持的性能拐点。记下这个最佳的 `--concurrency` 值（我们假设它是 `20`）。**

### 第3步：扩展到全服务器

现在我们已经找到了单卡的最佳配置，可以将它应用到所有8张卡上。

1.  **部署所有Workers**:
    *   使用 `systemd` 或其他工具，启动8个独立的 Celery Worker 服务。
    *   每个服务都使用你在第2步中找到的最佳并发数（例如 `20`）。
    *   确保每个服务都正确地绑定了独立的 `CUDA_VISIBLE_DEVICES`、`taskset` CPU范围 和 `-Q` 队列名。

    **Worker 0 的配置**: `--concurrency=20`, `CUDA_VISIBLE_DEVICES=0`, `taskset -c 0-23`, `-Q gpu0`
    **Worker 1 的配置**: `--concurrency=20`, `CUDA_VISIBLE_DEVICES=1`, `taskset -c 24-47`, `-Q gpu1`
    ...
    **Worker 7 的配置**: `--concurrency=20`, `CUDA_VISIBLE_DEVICES=7`, `taskset -c 168-191`, `-Q gpu7`

### 第4步：最终全链路压力测试

这是最后的决战，测试整个系统的极限。

1.  **启动大规模压测**:
    *   在 Locust 中，从一个较高的并发用户数开始，例如 `8 workers * 20 concurrency = 160` 个用户。
    *   逐步增加并发用户数（例如 `160 -> 200 -> 240 -> 300 -> 400 ...`）。

2.  **全面监控**:
    *   **Locust UI**: 紧盯 **RPS** 和 **响应时间**。特别关注 **95%** 和 **99%** 的延迟。
    *   `nvidia-smi`: 观察**所有8张GPU**的利用率是否均匀。
    *   `htop`: 观察**所有192个CPU核心**的总体使用情况。
    *   **API Server日志**: 检查是否有错误。
    *   **Worker日志**: 检查是否有任务失败或异常。

3.  **确定服务上限**:
    *   不断增加并发用户，直到你看到响应时间开始急剧恶化，或者错误率（Failures）开始上升。
    *   **能够稳定处理且延迟满足你预设目标（如95% < 500ms）的最大并发用户数，就是你这台服务器的最终性能指标。**

## 5. 结论

完成以上步骤后，你将得到一份关于你服务的详细性能报告，并为你的 Celery Worker 找到一个经过数据验证的最佳并发配置。这将确保你在生产环境中能够最大化地利用这台强大的H800服务器的计算资源。

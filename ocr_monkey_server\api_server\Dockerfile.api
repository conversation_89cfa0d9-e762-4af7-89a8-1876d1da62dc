# Stage 1: Build environment
# Use the specified Python 3.10 base image
FROM python:3.10-slim as builder

WORKDIR /app

# Copy only the requirements file first to leverage Docker cache
COPY api_requirements.txt .

# Install the python packages. The executables (like gunicorn) will be placed in /usr/local/bin
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r api_requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# Stage 2: Final, clean image
# Start from a fresh python 3.10 image to keep it small
FROM python:3.10-slim

# Add the workspace to the Python path to allow imports from common_lib
ENV PYTHONPATH "${PYTHONPATH}:/workspace"

# Set the working directory for the application
WORKDIR /workspace/ocr_monkey/api_server

# Copy the installed libraries from the builder stage
COPY --from=builder /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages
# ALSO copy the executables (like gunicorn, celery) from the builder stage
COPY --from=builder /usr/local/bin /usr/local/bin

# Now copy our application code and the common library into the final image
COPY . .

# Expose the new port
EXPOSE 50103

# The command to run the application will be provided by the deploy script


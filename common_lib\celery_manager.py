import asyncio
import itertools
import logging
from starlette.concurrency import run_in_threadpool

logger = logging.getLogger(__name__)

class CeleryQueueManager:
    """
    Manages discovery and round-robin scheduling of active Celery queues.
    """
    def __init__(self, celery_app):
        self.celery_app = celery_app
        self.active_queues_cache = []
        self.queue_scheduler = itertools.cycle(self.active_queues_cache)
        self._update_task = None

    def update_active_queues(self):
        """
        Inspects Celery to find active queues and updates the internal cache and scheduler.
        """
        logger.info("Inspecting for active Celery workers and queues...")
        try:
            inspection = self.celery_app.control.inspect(timeout=1)
            active_worker_queues = inspection.active_queues()

            if not active_worker_queues:
                logger.warning("No active workers found.")
                new_queues = []
            else:
                all_queues = {q['name'] for queues in active_worker_queues.values() for q in queues}
                new_queues = sorted(list(all_queues))

            if new_queues != self.active_queues_cache:
                logger.info(f"Active queues changed. Old: {self.active_queues_cache}, New: {new_queues}")
                self.active_queues_cache = new_queues
                self.queue_scheduler = itertools.cycle(self.active_queues_cache)
            else:
                logger.info(f"Active queues remain the same: {self.active_queues_cache}")

        except Exception as e:
            logger.error(f"Failed to inspect Celery workers: {e}", exc_info=True)
            if self.active_queues_cache:
                logger.warning("Clearing queue cache due to inspection error.")
                self.active_queues_cache = []
        
        return self.active_queues_cache

    def get_next_queue(self):
        """
        Returns the next available queue name in a round-robin fashion.
        Returns None if no queues are active.
        """
        if not self.active_queues_cache:
            return None
        return next(self.queue_scheduler)

    @property
    def has_active_queues(self) -> bool:
        """Returns True if there are active queues, False otherwise."""
        return len(self.active_queues_cache) > 0

    async def _periodic_updater(self, interval_seconds: int):
        """The background task that periodically calls the update logic."""
        while True:
            try:
                await run_in_threadpool(self.update_active_queues)
            except Exception as e:
                logger.error(f"Error in periodic queue updater: {e}", exc_info=True)
            await asyncio.sleep(interval_seconds)

    def start_background_updater(self, interval_seconds: int = 60):
        """
        Starts the background task to periodically update the list of active queues.
        """
        if self._update_task is None:
            logger.info(f"Starting background queue updater with {interval_seconds}s interval.")
            self._update_task = asyncio.create_task(self._periodic_updater(interval_seconds))
        return self._update_task

    def cancel_background_updater(self):
        """
        Stops the background queue updater task.
        """
        if self._update_task and not self._update_task.done():
            self._update_task.cancel()
            logger.info("Background queue updater task cancellation requested.")
        return self._update_task
